/**
 * 编辑器容器组件 - 参考ir-engine-dev实现
 */
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Layout, Button, Tooltip, Space, Dropdown, Modal, Input, Form } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ArrowsAltOutlined,
  <PERSON>otateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  DotChartOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo
} from '../../store/editor/editorSlice';
import { toggleFullscreen, PanelType } from '../../store/ui/uiSlice';
import {
  setLayout,
  saveLayout,
  loadLayout,
  resetLayout,
  toggleTheme,
  defaultLayout
} from '../../store/ui/layoutSlice';
import { setShowGitPanel } from '../../store/git/gitSlice';

import Toolbar from '../toolbar/Toolbar';
import DockLayout, { DockLayoutRef } from './DockLayout';
import MobileAdaptiveLayout from './MobileAdaptiveLayout';
import LayoutService from '../../services/LayoutService';
import MobileDeviceService from '../../services/MobileDeviceService';
import { registerPanelComponent } from '../panels/PanelRegistry';
import ScenePanel from '../panels/ScenePanel';
import HierarchyPanel from '../panels/HierarchyPanel';
import InspectorPanel from '../panels/InspectorPanel';
import AssetsPanel from '../panels/AssetsPanel';
import ConsolePanel from '../panels/ConsolePanel';
import CollaborationPanel from '../collaboration/CollaborationPanel';
import UserTestingPanel from '../testing/UserTestingPanel';
import DebugPanel from '../debug/DebugPanel';
import PerformanceOptimizationPanel from '../optimization/PerformanceOptimizationPanel';
import ResourceHotUpdatePanel from '../resources/ResourceHotUpdatePanel';

import { LayoutData, TabData, DockMode } from 'rc-dock';
import { ErrorBoundary } from '../common/ErrorBoundary';

// 引入面板Tab定义
import { ViewportPanelTab } from '../panels/ViewportPanelTab';
import { HierarchyPanelTab } from '../panels/HierarchyPanelTab';
import { AssetsPanelTab } from '../panels/AssetsPanelTab';
import { PropertiesPanelTab } from '../panels/PropertiesPanelTab';
import { MaterialsPanelTab } from '../panels/MaterialsPanelTab';
import { ScenePanelTab } from '../panels/ScenePanelTab';
import { InspectorPanelTab } from '../panels/InspectorPanelTab';

// 引入样式
import './EditorContainer.css';

// DockContainer组件 - 参考ir-engine-dev实现
export const DockContainer = ({ children, id = 'editor-dock', dividerAlpha = 0 }) => {
  const dockContainerStyles = {
    '--dividerAlpha': dividerAlpha
  };

  return (
    <div id={id} className="dock-container" style={dockContainerStyles as React.CSSProperties}>
      {children}
    </div>
  );
};

// 活动下方面板类型
type ActiveLowerPanel = 'propertiesPanel' | 'inspectorPanel';

// 默认布局配置 - 参考ir-engine-dev实现
const defaultLayout = (flags: {
  visualScriptPanelEnabled: boolean;
  activeLowerPanel: ActiveLowerPanel;
}): LayoutData => {
  const tabs = [AssetsPanelTab];
  if (flags.visualScriptPanelEnabled) {
    // 可以添加VisualScriptPanelTab
  }
  const activeLowerPane = flags.activeLowerPanel;

  return {
    dockbox: {
      mode: 'horizontal' as DockMode,
      children: [
        {
          mode: 'vertical' as DockMode,
          size: 8,
          children: [
            {
              tabs: [ViewportPanelTab]
            },
            {
              tabs: tabs
            }
          ]
        },
        {
          mode: 'vertical' as DockMode,
          size: 3,
          children: [
            {
              tabs: [HierarchyPanelTab, ScenePanelTab, MaterialsPanelTab]
            },
            {
              tabs: [PropertiesPanelTab, InspectorPanelTab],
              activeId: activeLowerPane
            }
          ]
        }
      ]
    }
  };
};

interface EditorLayoutProps {
  projectId: string;
  sceneId: string;
}

export const EditorLayout: React.FC<EditorLayoutProps> = ({ projectId, sceneId }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const dockLayoutRef = useRef<DockLayout>(null);

  // 状态管理
  const [visualScriptPanelEnabled] = useState(false);
  const [activeLowerPanel] = useState<ActiveLowerPanel>('propertiesPanel');

  // 记忆化默认布局
  const memoizedDefaultLayout = useMemo(() => {
    return defaultLayout({
      visualScriptPanelEnabled,
      activeLowerPanel
    });
  }, [visualScriptPanelEnabled, activeLowerPanel]);

  // 简化状态获取，添加错误处理
  const editorState = useAppSelector((state) => {
    try {
      return state?.editor || {
        transformMode: TransformMode.TRANSLATE,
        transformSpace: TransformSpace.LOCAL,
        snapMode: SnapMode.DISABLED,
        showGrid: true,
        showAxes: true,
        isPlaying: false
      };
    } catch (error) {
      console.warn('Editor state access error:', error);
      return {
        transformMode: TransformMode.TRANSLATE,
        transformSpace: TransformSpace.LOCAL,
        snapMode: SnapMode.DISABLED,
        showGrid: true,
        showAxes: true,
        isPlaying: false
      };
    }
  });

  const uiState = useAppSelector((state) => {
    try {
      return state?.ui || { fullscreen: false, theme: 'light', savedLayouts: {} };
    } catch (error) {
      console.warn('UI state access error:', error);
      return { fullscreen: false, theme: 'light', savedLayouts: {} };
    }
  });

  const {
    transformMode,
    transformSpace,
    snapMode,
    showGrid,
    showAxes,
    isPlaying
  } = editorState;

  const { fullscreen, theme, savedLayouts } = uiState;

  const [saveLayoutModalVisible, setSaveLayoutModalVisible] = useState(false);
  const [layoutName, setLayoutName] = useState('');
  const [form] = Form.useForm();

  // 初始化布局服务 - 添加更健壮的错误处理
  useEffect(() => {
    const initializeLayoutService = async () => {
      try {
        console.log('🔧 初始化编辑器布局服务...');

        // 延迟设置DockLayout引用，确保组件已完全挂载
        setTimeout(() => {
          try {
            if (dockLayoutRef.current) {
              LayoutService.getInstance().setDockLayoutRef(dockLayoutRef.current);
              console.log('✅ DockLayout引用设置成功');
            } else {
              console.warn('⚠️ DockLayout引用尚未准备就绪，将在下次渲染时重试');
            }
          } catch (refError) {
            console.error('❌ 设置DockLayout引用失败:', refError);
          }
        }, 100);

        // 注册面板组件 - 添加错误处理
        console.log('📋 注册面板组件...');
        try {
          registerPanelComponent(PanelType.HIERARCHY, HierarchyPanel);
          registerPanelComponent(PanelType.INSPECTOR, InspectorPanel);
          registerPanelComponent(PanelType.SCENE, ScenePanel);
          registerPanelComponent(PanelType.ASSETS, AssetsPanel);
          registerPanelComponent(PanelType.CONSOLE, ConsolePanel);
          registerPanelComponent(PanelType.COLLABORATION, CollaborationPanel);
          registerPanelComponent(PanelType.USER_TESTING, UserTestingPanel);
          registerPanelComponent(PanelType.DEBUG, DebugPanel);
          registerPanelComponent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);
          console.log('✅ 面板组件注册完成');
        } catch (panelError) {
          console.error('❌ 面板组件注册失败:', panelError);
          // 继续执行，不阻止布局初始化
        }

        // 注册面板内容组件到布局服务 - 添加错误处理
        console.log('🔗 注册面板内容组件...');
        try {
          LayoutService.registerPanelContent(PanelType.HIERARCHY, HierarchyPanel);
          LayoutService.registerPanelContent(PanelType.INSPECTOR, InspectorPanel);
          LayoutService.registerPanelContent(PanelType.SCENE, ScenePanel);
          LayoutService.registerPanelContent(PanelType.ASSETS, AssetsPanel);
          LayoutService.registerPanelContent(PanelType.CONSOLE, ConsolePanel);
          LayoutService.registerPanelContent(PanelType.COLLABORATION, CollaborationPanel);
          LayoutService.registerPanelContent(PanelType.USER_TESTING, UserTestingPanel);
          LayoutService.registerPanelContent(PanelType.DEBUG, DebugPanel);
          LayoutService.registerPanelContent(PanelType.PERFORMANCE_OPTIMIZATION, PerformanceOptimizationPanel);
          LayoutService.registerPanelContent(PanelType.RESOURCE_HOT_UPDATE, ResourceHotUpdatePanel);
          console.log('✅ 面板内容组件注册完成');
        } catch (contentError) {
          console.error('❌ 面板内容组件注册失败:', contentError);
          // 继续执行，不阻止布局初始化
        }

        console.log('✅ 编辑器布局服务初始化完成');
      } catch (error) {
        console.error('❌ 编辑器布局服务初始化失败:', error);
        // 即使初始化失败，也不应该阻止组件渲染
        // 可以显示一个降级的UI或错误提示
      }
    };

    initializeLayoutService();
  }, [dockLayoutRef]);

  // 切换全屏
  const handleToggleFullscreen = () => {
    dispatch(toggleFullscreen());

    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  // 切换播放状态
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 设置变换模式
  const handleSetTransformMode = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
  };

  // 设置变换空间
  const handleSetTransformSpace = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
  };

  // 设置网格捕捉模式
  const handleSetSnapMode = (mode: SnapMode) => {
    dispatch(setSnapMode(mode));
  };

  // 切换网格显示
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 切换坐标轴显示
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 撤销操作
  const handleUndo = () => {
    dispatch(undo());
  };

  // 重做操作
  const handleRedo = () => {
    dispatch(redo());
  };

  // 处理布局变化
  const handleLayoutChange = (newLayout: LayoutData) => {
    dispatch(setLayout(newLayout));
  };

  // 重置布局
  const handleResetLayout = () => {
    dispatch(resetLayout());
    if (dockLayoutRef.current) {
      dockLayoutRef.current.loadLayout(defaultLayout);
    }
  };

  // 切换主题
  const handleToggleTheme = () => {
    dispatch(toggleTheme());
  };

  // 打开保存布局对话框
  const handleOpenSaveLayoutModal = useCallback(() => {
    try {
      setLayoutName('');
      form.resetFields();
      setSaveLayoutModalVisible(true);
    } catch (error) {
      console.error('打开保存布局对话框失败:', error);
    }
  }, [form]);

  // 保存布局
  const handleSaveLayout = useCallback(() => {
    try {
      if (!layoutName.trim()) {
        return;
      }

      if (dockLayoutRef.current) {
        const currentLayout = dockLayoutRef.current.saveLayout() as LayoutData;
        dispatch(saveLayout({ name: layoutName, layout: currentLayout }));
        setSaveLayoutModalVisible(false);
      }
    } catch (error) {
      console.error('保存布局失败:', error);
    }
  }, [layoutName, dispatch]);

  // 关闭保存布局对话框
  const handleCloseSaveLayoutModal = useCallback(() => {
    try {
      setSaveLayoutModalVisible(false);
      setLayoutName('');
      form.resetFields();
    } catch (error) {
      console.error('关闭保存布局对话框失败:', error);
    }
  }, [form]);

  // 加载布局
  const handleLoadLayout = (layoutName: string) => {
    dispatch(loadLayout(layoutName));
    if (dockLayoutRef.current && savedLayouts[layoutName]) {
      dockLayoutRef.current.loadLayout(savedLayouts[layoutName]);
    }
  };

  // 打开面板
  const handleOpenPanel = useCallback((panelType: string) => {
    try {
      if (!dockLayoutRef.current) {
        console.warn('DockLayout引用未准备就绪');
        return;
      }

      // 获取当前布局
      const currentLayout = dockLayoutRef.current.saveLayout();
      if (!currentLayout || !currentLayout.dockbox) {
        console.warn('无法获取当前布局');
        return;
      }

      // 检查面板是否已经打开（通过遍历布局查找）
      const findPanelInLayout = (layout: any): boolean => {
        if (!layout) return false;

        // 检查 dockbox
        if (layout.dockbox?.children) {
          for (const child of layout.dockbox.children) {
            if (findPanelInChildren(child)) return true;
          }
        }

        // 检查 floatbox
        if (layout.floatbox?.children) {
          for (const child of layout.floatbox.children) {
            if (findPanelInChildren(child)) return true;
          }
        }

        return false;
      };

      const findPanelInChildren = (node: any): boolean => {
        if (!node) return false;

        // 检查当前节点的 tabs
        if (node.tabs) {
          for (const tab of node.tabs) {
            if (tab.id === panelType) return true;
          }
        }

        // 递归检查子节点
        if (node.children) {
          for (const child of node.children) {
            if (findPanelInChildren(child)) return true;
          }
        }

        return false;
      };

      if (findPanelInLayout(currentLayout)) {
        console.log(`面板 ${panelType} 已经打开`);
        return;
      }

      // 创建新的面板标签
      const newTab = {
        id: panelType,
        title: panelType,
        content: panelType as any,
        closable: true
      };

      // 找到右侧面板组（通常是第三个子面板）
      const dockbox = currentLayout.dockbox;
      if (dockbox.children && dockbox.children.length >= 3) {
        const rightPanel = dockbox.children[2];
        if (rightPanel && rightPanel.children && rightPanel.children.length > 0) {
          const firstGroup = rightPanel.children[0];
          if (firstGroup && firstGroup.tabs) {
            // 将新标签添加到右侧第一个标签组
            firstGroup.tabs.push(newTab);
            // 重新加载布局
            dockLayoutRef.current.loadLayout(currentLayout);
            console.log(`成功打开面板: ${panelType}`);
          }
        }
      }
    } catch (error) {
      console.error('打开面板失败:', error);
    }
  }, []);



  // 布局菜单项
  const layoutMenuItems = [
    {
      key: 'reset',
      label: t('editor.layout.reset') || '重置布局',
      icon: <ReloadOutlined />,
      onClick: handleResetLayout
    },
    {
      key: 'save',
      label: t('editor.layout.save') || '保存布局',
      icon: <SaveOutlined />,
      onClick: handleOpenSaveLayoutModal
    },
    {
      key: 'theme',
      label: theme === 'light' ? (t('editor.layout.darkTheme') || '暗色主题') : (t('editor.layout.lightTheme') || '亮色主题'),
      icon: theme === 'light' ? <EyeInvisibleOutlined /> : <EyeOutlined />,
      onClick: handleToggleTheme
    },
    {
      type: 'divider' as const
    },
    {
      key: 'layouts',
      label: t('editor.layout.loadLayout') || '加载布局',
      children: Object.keys(savedLayouts).map(name => ({
        key: `layout-${name}`,
        label: name,
        onClick: () => handleLoadLayout(name)
      }))
    }
  ];

  // 检查是否是移动设备
  const isMobileDevice = MobileDeviceService.isMobileDevice() || MobileDeviceService.isTabletDevice();

  return (
    <main className="pointer-events-auto">
      <div id="editor-container" className="flex flex-col" style={{ height: '100vh' }}>
        {/* 顶部工具栏 */}
        <div style={{ height: '40px', background: '#1e1e1e' }}>
          <Toolbar onOpenPanel={handleOpenPanel} />
        </div>

        {/* 主编辑器区域 */}
        <div className="mt-1 flex overflow-hidden" style={{ height: 'calc(100vh - 40px)' }}>
          <DockContainer>
            <DockLayout
              ref={dockLayoutRef}
              defaultLayout={memoizedDefaultLayout}
              style={{
                position: 'absolute',
                left: 5,
                top: 5,
                right: 5,
                bottom: 5
              }}
            />
          </DockContainer>
        </div>
      </div>

      {/* 保存布局模态框 */}
      <Modal
        title={t('editor.layout.saveLayout') || '保存布局'}
        open={saveLayoutModalVisible}
        onOk={handleSaveLayout}
        onCancel={() => setSaveLayoutModalVisible(false)}
        okText={t('common.save') || '保存'}
        cancelText={t('common.cancel') || '取消'}
      >
        <Form form={saveLayoutForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layout.layoutName') || '布局名称'}
            rules={[
              { required: true, message: t('editor.layout.layoutNameRequired') || '请输入布局名称' },
              { max: 50, message: t('editor.layout.layoutNameTooLong') || '布局名称不能超过50个字符' }
            ]}
          >
            <Input placeholder={t('editor.layout.layoutNamePlaceholder') || '请输入布局名称'} />
          </Form.Item>
        </Form>
      </Modal>
    </main>
  );
};
