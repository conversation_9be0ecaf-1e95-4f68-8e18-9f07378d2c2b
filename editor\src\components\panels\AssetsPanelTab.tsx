/**
 * 资源面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { FolderOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import AssetsPanel from './AssetsPanel';

// 面板标题组件
const AssetsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <FolderOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.assets') || 'Assets'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const AssetsPanelTab: TabData = {
  id: 'assetsPanel',
  closable: true,
  title: <AssetsPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Assets tab</div>}>
      <Suspense fallback={<div>Loading Assets...</div>}>
        <AssetsPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default AssetsPanelTab;
