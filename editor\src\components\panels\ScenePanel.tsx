/**
 * 场景面板组件 - 参考ir-engine-dev的ViewportContainer实现
 */
import React, { useRef, useEffect, useState } from 'react';
import { Button, Select, Tooltip, Space, Dropdown } from 'antd';
import {
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SettingOutlined,
  FullscreenOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setViewportSize,
  setActiveCamera,
  setSceneGraph,
  setTransformMode,
  setTransformSpace,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace
} from '../../store/editor/editorSlice';

// 导入服务
import EngineService from '../../services/EngineService';
import SceneService, { SceneEventType } from '../../services/SceneService';

// 导入引擎相关类型和函数

const ScenePanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    showGrid,
    showAxes,
    isPlaying,
    transformMode,
    transformSpace} = useAppSelector((state) => state.editor);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [isEngineInitialized, setIsEngineInitialized] = useState(false);
  const [isSceneLoaded, setIsSceneLoaded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });
  const [engineError, setEngineError] = useState<string | null>(null);
  const [fps, setFps] = useState(0);

  // 初始化引擎
  useEffect(() => {
    if (!canvasRef.current || isEngineInitialized) return;

    const initEngine = async () => {
      try {
        if (!canvasRef.current) {
          console.error('Canvas 元素不存在');
          return;
        }

        // 初始化引擎
        await EngineService.initialize(canvasRef.current, {
          autoStart: true,
          debug: true
        });

        setIsEngineInitialized(true);

        // 启动引擎
        EngineService.start();

        // 设置默认场景配置
        const scene = EngineService.getActiveScene();
        if (scene) {
          console.log('场景配置已设置');
        }

        // 监听引擎事件（如果引擎支持事件系统）
        // EngineService.on?.(EngineEventType.OBJECT_SELECTED, handleObjectSelected);
        // EngineService.on?.(EngineEventType.OBJECT_DESELECTED, handleObjectDeselected);

        console.log('引擎初始化成功');
        setEngineError(null);
      } catch (error) {
        console.error('引擎初始化失败:', error);
        setEngineError(error instanceof Error ? error.message : '引擎初始化失败');
      }
    };

    initEngine();

    // 清理函数
    return () => {
      // 清理引擎事件监听器（如果引擎支持事件系统）
      // EngineService.off?.(EngineEventType.OBJECT_SELECTED, handleObjectSelected);
      // EngineService.off?.(EngineEventType.OBJECT_DESELECTED, handleObjectDeselected);
    };
  }, [canvasRef]);

  // 监听场景服务事件
  useEffect(() => {
    // 监听场景加载事件
    SceneService.on(SceneEventType.LOADING_COMPLETE, handleSceneLoaded);
    SceneService.on(SceneEventType.SCENE_GRAPH_CHANGED, handleSceneGraphChanged);

    // 清理函数
    return () => {
      SceneService.off(SceneEventType.LOADING_COMPLETE, handleSceneLoaded);
      SceneService.off(SceneEventType.SCENE_GRAPH_CHANGED, handleSceneGraphChanged);
    };
  }, []);

  // 处理窗口大小变化
  useEffect(() => {
    if (!containerRef.current || !isEngineInitialized) return;

    const handleResize = () => {
      if (!containerRef.current) return;

      const { width, height } = containerRef.current.getBoundingClientRect();

      // 更新Redux状态
      dispatch(setViewportSize({ width, height }));
    };

    // 初始调整大小
    handleResize();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [containerRef, isEngineInitialized, dispatch]);

  // FPS监控
  useEffect(() => {
    if (!isEngineInitialized) return;

    let frameCount = 0;
    let lastTime = performance.now();

    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      if (isEngineInitialized) {
        requestAnimationFrame(updateFPS);
      }
    };

    const fpsTimer = requestAnimationFrame(updateFPS);

    return () => {
      cancelAnimationFrame(fpsTimer);
    };
  }, [isEngineInitialized]);

  // 键盘快捷键处理
  useEffect(() => {
    if (!isEngineInitialized) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case 'w':
          // 切换到移动模式
          EngineService.setTransformMode(TransformMode.TRANSLATE);
          break;
        case 'e':
          // 切换到旋转模式
          EngineService.setTransformMode(TransformMode.ROTATE);
          break;
        case 'r':
          // 切换到缩放模式
          EngineService.setTransformMode(TransformMode.SCALE);
          break;
        case 'q':
          // 切换变换空间
          const currentSpace = transformSpace;
          const newSpace = currentSpace === TransformSpace.LOCAL ? TransformSpace.WORLD : TransformSpace.LOCAL;
          EngineService.setTransformSpace(newSpace);
          break;
        case 'delete':
        case 'backspace':
          // 删除选中对象
          try {
            const selectedEntities = EngineService.getSelectedEntities?.() || [];
            selectedEntities.forEach(entity => {
              EngineService.removeEntity?.(entity);
            });
          } catch (error) {
            console.warn('删除对象失败:', error);
          }
          break;
        case 'f':
          // 聚焦到选中对象
          try {
            const selectedEntities = EngineService.getSelectedEntities?.() || [];
            if (selectedEntities.length > 0) {
              // EngineService.focusOnEntity?.(selectedEntities[0]);
              console.log('聚焦到对象:', selectedEntities[0]);
            }
          } catch (error) {
            console.warn('聚焦对象失败:', error);
          }
          break;
        case 'g':
          // 网格显示切换
          // dispatch(toggleGrid());
          break;
        case 'x':
          // 坐标轴显示切换
          // dispatch(toggleAxes());
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEngineInitialized, transformSpace]);

  // 处理网格显示
  useEffect(() => {
    if (!isSceneLoaded) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      try {
        // 尝试设置网格显示
        if (typeof (scene as any).setGridVisible === 'function') {
          (scene as any).setGridVisible(showGrid);
        } else {
          // 如果场景没有直接的网格控制方法，可以通过其他方式实现
          console.log('设置网格显示:', showGrid);
          // 可以在这里添加创建/移除网格对象的逻辑
          // const gridHelper = scene.getObjectByName?.('grid');
          // if (showGrid && !gridHelper) {
          //   // 创建网格
          //   const grid = createGridHelper();
          //   scene.add?.(grid);
          // } else if (!showGrid && gridHelper) {
          //   // 移除网格
          //   scene.remove?.(gridHelper);
          // }
        }
      } catch (error) {
        console.warn('设置网格显示失败:', error);
      }
    }
  }, [isSceneLoaded, showGrid]);

  // 处理坐标轴显示
  useEffect(() => {
    if (!isSceneLoaded) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      try {
        // 尝试设置坐标轴显示
        if (typeof (scene as any).setAxesVisible === 'function') {
          (scene as any).setAxesVisible(showAxes);
        } else {
          // 如果场景没有直接的坐标轴控制方法，可以通过其他方式实现
          console.log('设置坐标轴显示:', showAxes);
          // 可以在这里添加创建/移除坐标轴对象的逻辑
          // const axesHelper = scene.getObjectByName?.('axes');
          // if (showAxes && !axesHelper) {
          //   // 创建坐标轴
          //   const axes = createAxesHelper();
          //   scene.add?.(axes);
          // } else if (!showAxes && axesHelper) {
          //   // 移除坐标轴
          //   scene.remove?.(axesHelper);
          // }
        }
      } catch (error) {
        console.warn('设置坐标轴显示失败:', error);
      }
    }
  }, [isSceneLoaded, showAxes]);

  // 处理播放状态
  useEffect(() => {
    if (!isEngineInitialized) return;

    if (isPlaying) {
      EngineService.start();
    } else {
      EngineService.stop();
    }
  }, [isEngineInitialized, isPlaying]);

  // 处理变换模式
  useEffect(() => {
    if (!isEngineInitialized) return;

    EngineService.setTransformMode(transformMode as TransformMode);
  }, [isEngineInitialized, transformMode]);

  // 处理变换空间
  useEffect(() => {
    if (!isEngineInitialized) return;

    EngineService.setTransformSpace(transformSpace as TransformSpace);
  }, [isEngineInitialized, transformSpace]);

  // 处理对象选择（暂时未使用，等待引擎事件系统实现）
  // const handleObjectSelected = (entity: any) => {
  //   // 更新Redux状态
  //   dispatch(setSelectedObject(entity));

  //   // 如果是相机，设置为活动相机
  //   const camera = entity.getComponent?.('Camera');
  //   if (camera) {
  //     dispatch(setActiveCamera(camera));
  //     EngineService.setActiveCamera?.(camera);
  //   }
  // };

  // 处理对象取消选择（暂时未使用，等待引擎事件系统实现）
  // const handleObjectDeselected = (_entity: any) => {
  //   // 更新Redux状态
  //   const selectedEntities = EngineService.getSelectedEntities?.() || [];
  //   if (selectedEntities.length === 0) {
  //     dispatch(setSelectedObject(null));
  //   } else {
  //     dispatch(setSelectedObjects(selectedEntities));
  //   }
  // };

  // 处理场景加载
  const handleSceneLoaded = (_data: any) => {
    setIsSceneLoaded(true);

    // 获取活动相机
    const camera = EngineService.getActiveCamera();
    if (camera) {
      dispatch(setActiveCamera(camera));
    }

    // 开始引擎渲染
    EngineService.start();
  };

  // 处理场景图变化
  const handleSceneGraphChanged = (sceneGraph: any) => {
    dispatch(setSceneGraph(sceneGraph));
  };

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isSceneLoaded) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setLastMousePosition({ x, y });
    setIsDragging(true);

    // 左键点击 - 对象选择
    if (e.button === 0) {
      // 将坐标转换为归一化设备坐标 (-1 到 1)
      const normalizedX = (x / rect.width) * 2 - 1;
      const normalizedY = -(y / rect.height) * 2 + 1;

      // 使用引擎的射线投射功能选择对象
      const scene = EngineService.getActiveScene();
      if (scene) {
        const camera = EngineService.getActiveCamera();
        if (camera) {
          console.log('鼠标点击坐标:', { x, y, normalizedX, normalizedY });
          // 实际的射线投射逻辑
          try {
            // const hitResult = scene.raycast?.(normalizedX, normalizedY, camera);
            // if (hitResult && hitResult.entity) {
            //   EngineService.selectEntity(hitResult.entity);
            // } else {
            //   EngineService.clearSelection();
            // }
          } catch (error) {
            console.warn('射线投射失败:', error);
          }
        }
      }
    }
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isSceneLoaded || !isDragging) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const deltaX = x - lastMousePosition.x;
    const deltaY = y - lastMousePosition.y;

    // 右键拖拽 - 相机旋转
    if (e.buttons === 2) {
      const camera = EngineService.getActiveCamera();
      if (camera) {
        // 实现相机旋转逻辑
        const sensitivity = 0.005;
        try {
          // camera.rotateY?.(-deltaX * sensitivity);
          // camera.rotateX?.(-deltaY * sensitivity);
          console.log('相机旋转:', { deltaX: -deltaX * sensitivity, deltaY: -deltaY * sensitivity });
        } catch (error) {
          console.warn('相机旋转失败:', error);
        }
      }
    }

    // 中键拖拽 - 相机平移
    if (e.buttons === 4) {
      const camera = EngineService.getActiveCamera();
      if (camera) {
        // 实现相机平移逻辑
        const sensitivity = 0.01;
        try {
          // camera.translateX?.(deltaX * sensitivity);
          // camera.translateY?.(-deltaY * sensitivity);
          console.log('相机平移:', { deltaX: deltaX * sensitivity, deltaY: -deltaY * sensitivity });
        } catch (error) {
          console.warn('相机平移失败:', error);
        }
      }
    }

    setLastMousePosition({ x, y });
  };

  // 处理鼠标抬起事件
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理鼠标滚轮事件
  const handleWheel = (e: React.WheelEvent) => {
    if (!isSceneLoaded) return;

    e.preventDefault();

    const camera = EngineService.getActiveCamera();
    if (camera) {
      // 实现相机缩放逻辑
      const sensitivity = 0.1;
      const zoomDelta = e.deltaY * sensitivity;

      try {
        // camera.zoom?.(zoomDelta);
        console.log('相机缩放:', { zoomDelta });
      } catch (error) {
        console.warn('相机缩放失败:', error);
      }
    }
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止默认右键菜单
  };

  // 视口工具栏组件
  const ViewportToolbar = () => (
    <div style={{
      position: 'relative',
      zIndex: 20,
      background: '#2d2d30',
      padding: '12px 20px',
      borderBottom: '1px solid #3e3e42'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', gap: '4px' }}>
        {/* 左侧工具 */}
        <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '20px' }}>
          <Space>
            <Tooltip title="Transform Space">
              <Select
                value={transformSpace}
                onChange={(value) => dispatch(setTransformSpace(value))}
                style={{ width: 80 }}
                size="small"
                options={[
                  { value: TransformSpace.LOCAL, label: 'Local' },
                  { value: TransformSpace.WORLD, label: 'World' }
                ]}
              />
            </Tooltip>

            <Tooltip title="Transform Mode">
              <Space.Compact>
                <Button
                  size="small"
                  type={transformMode === TransformMode.TRANSLATE ? 'primary' : 'default'}
                  icon={<ArrowsAltOutlined />}
                  onClick={() => dispatch(setTransformMode(TransformMode.TRANSLATE))}
                />
                <Button
                  size="small"
                  type={transformMode === TransformMode.ROTATE ? 'primary' : 'default'}
                  icon={<RotateRightOutlined />}
                  onClick={() => dispatch(setTransformMode(TransformMode.ROTATE))}
                />
                <Button
                  size="small"
                  type={transformMode === TransformMode.SCALE ? 'primary' : 'default'}
                  icon={<ColumnWidthOutlined />}
                  onClick={() => dispatch(setTransformMode(TransformMode.SCALE))}
                />
              </Space.Compact>
            </Tooltip>
          </Space>
        </div>

        {/* 右侧工具 */}
        <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '20px' }}>
          <Space>
            <Tooltip title="Scene Helpers">
              <Space.Compact>
                <Button
                  size="small"
                  type={showGrid ? 'primary' : 'default'}
                  icon={<BorderOutlined />}
                  onClick={() => dispatch(setShowGrid(!showGrid))}
                >
                  Grid
                </Button>
                <Button
                  size="small"
                  type={showAxes ? 'primary' : 'default'}
                  icon={<EyeOutlined />}
                  onClick={() => dispatch(setShowAxes(!showAxes))}
                >
                  Axes
                </Button>
              </Space.Compact>
            </Tooltip>

            <Tooltip title="Render Mode">
              <Button size="small" icon={<SettingOutlined />}>
                Render
              </Button>
            </Tooltip>

            <Tooltip title="Scene Playback">
              <Button
                size="small"
                type={isPlaying ? 'primary' : 'default'}
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => dispatch(setIsPlaying(!isPlaying))}
              />
            </Tooltip>
          </Space>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{ position: 'relative', zIndex: 30, display: 'flex', height: '100%', width: '100%', flexDirection: 'column' }}>
      {/* 视口工具栏 */}
      <ViewportToolbar />

      {/* 主视口区域 */}
      <div
        ref={containerRef}
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          background: '#1e1e1e',
          overflow: 'hidden'
        }}
      >
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            display: 'block'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onWheel={handleWheel}
        onContextMenu={handleContextMenu}
      />
      {!isEngineInitialized && !engineError && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            color: '#fff',
            backgroundColor: 'rgba(0, 0, 0, 0.5)'}}
        >
          {t('editor.loadingEngine')}
        </div>
      )}

      {engineError && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            color: '#ff4d4f',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            padding: '20px',
            textAlign: 'center'}}
        >
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>
            {t('editor.engineError')}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.8 }}>
            {engineError}
          </div>
        </div>
      )}

      {isEngineInitialized && !engineError && (
        <div
          style={{
            position: 'absolute',
            top: '50px',
            right: '10px',
            color: '#fff',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            padding: '5px 10px',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'monospace'}}
        >
          FPS: {fps}
        </div>
      )}
    </div>
  );
};

export default ScenePanel;
