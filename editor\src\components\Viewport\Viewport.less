/**
 * 视口样式
 */
.viewport-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 顶部工具栏
  .viewport-top-toolbar {
    height: 32px;
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    flex-shrink: 0;

    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .toolbar-center {
      .viewport-info {
        color: #cccccc;
        font-size: 12px;
      }
    }

    .ant-btn {
      color: #cccccc;
      border-color: #3e3e42;
      background: transparent;
      font-size: 11px;
      height: 24px;
      padding: 0 8px;

      &:hover {
        color: #fff;
        border-color: #007acc;
        background: rgba(0, 122, 204, 0.1);
      }

      &.ant-btn-primary {
        color: #fff;
        background: #007acc;
        border-color: #007acc;
      }
    }
  }

  .viewport-canvas {
    flex: 1;
    width: 100%;
    display: block;
  }
  
  .viewport-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .toolbar-group {
      display: flex;
      gap: 4px;
      background: rgba(0, 0, 0, 0.5);
      padding: 4px;
      border-radius: 4px;
      
      .ant-btn {
        color: #fff;
        border-color: #444;
        background: rgba(0, 0, 0, 0.3);
        
        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
        
        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }
  
  .viewport-status {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 8px;
    border-radius: 4px;
    color: #fff;
    
    .status-info {
      font-size: 12px;
    }
  }
}
