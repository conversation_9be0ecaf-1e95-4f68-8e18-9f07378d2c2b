/**
 * 视口面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { ErrorBoundary } from '../common/ErrorBoundary';
import ScenePanel from './ScenePanel';

// 面板标题组件
const ViewportPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none'
    }}>
      <span>{t('editor.viewport') || 'Viewport'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const ViewportPanelTab: TabData = {
  id: 'viewportPanel',
  closable: false, // 视口面板不可关闭
  title: <ViewportPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Viewport tab</div>}>
      <Suspense fallback={<div>Loading Viewport...</div>}>
        <ScenePanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default ViewportPanelTab;
