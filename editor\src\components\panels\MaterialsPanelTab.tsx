/**
 * 材质面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { AppstoreOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import MaterialLibraryPanel from './MaterialLibraryPanel';

// 面板标题组件
const MaterialsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <AppstoreOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.materials') || 'Materials'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const MaterialsPanelTab: TabData = {
  id: 'materialsPanel',
  closable: true,
  title: <MaterialsPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Materials tab</div>}>
      <Suspense fallback={<div>Loading Materials...</div>}>
        <MaterialLibraryPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default MaterialsPanelTab;
