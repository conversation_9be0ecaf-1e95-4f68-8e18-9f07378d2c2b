/**
 * 编辑器布局位置修复验证脚本
 * 验证组件位置调整是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 编辑器布局位置修复验证开始...\n');

// 验证项目列表
const verificationItems = [
  {
    name: '编辑器主页面布局调整',
    file: 'editor/src/pages/Editor/index.tsx',
    checks: [
      { pattern: /editor-left-panels/, description: '左侧面板区域容器' },
      { pattern: /left-panel-top/, description: '左侧上部面板（Hierarchy）' },
      { pattern: /left-panel-bottom/, description: '左侧下部面板（Material Library）' },
      { pattern: /HierarchyPanel/, description: 'Hierarchy面板在左侧上部' },
      { pattern: /MaterialLibraryPanel/, description: 'Material Library面板在左侧下部' },
      { pattern: /editor\.hierarchy.*Hierarchy/, description: 'Hierarchy标题' },
      { pattern: /editor\.materialLibrary.*Material Library/, description: 'Material Library标题' },
      { pattern: /editor\.viewport.*Viewport/, description: '右侧Viewport标签页' }
    ]
  },
  {
    name: '编辑器样式文件更新',
    file: 'editor/src/pages/Editor/Editor.less',
    checks: [
      { pattern: /editor-left-panels/, description: '左侧面板区域样式' },
      { pattern: /left-panel-top/, description: '左侧上部面板样式' },
      { pattern: /left-panel-bottom/, description: '左侧下部面板样式' },
      { pattern: /width: 250px/, description: '左侧面板宽度设置' },
      { pattern: /flex: 1/, description: '上部面板自适应高度' },
      { pattern: /height: 300px/, description: '下部面板固定高度' }
    ]
  },
  {
    name: '中文翻译文件',
    file: 'editor/src/i18n/locales/zh-CN.json',
    checks: [
      { pattern: /"viewport": "视口"/, description: 'Viewport标签翻译' },
      { pattern: /"viewportSettings": "视口设置"/, description: '视口设置翻译' }
    ]
  },
  {
    name: '底部资源面板',
    file: 'editor/src/components/panels/BottomAssetsPanel.tsx',
    checks: [
      { pattern: /editor\.assets.*资源/, description: 'Assets标签页' },
      { pattern: /editor\.visualScripting.*可视化脚本/, description: 'Visual Scripting标签页' },
      { pattern: /renderAssetGrid/, description: '资源网格渲染' }
    ]
  }
];

let totalChecks = 0;
let passedChecks = 0;
let failedItems = [];

// 执行验证
verificationItems.forEach(item => {
  console.log(`📋 验证: ${item.name}`);
  
  const filePath = path.join(__dirname, item.file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${item.file}`);
    failedItems.push(`${item.name}: 文件不存在`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  item.checks.forEach(check => {
    totalChecks++;
    if (check.pattern.test(content)) {
      console.log(`  ✅ ${check.description}`);
      passedChecks++;
    } else {
      console.log(`  ❌ ${check.description}`);
      failedItems.push(`${item.name}: ${check.description}`);
    }
  });
  
  console.log('');
});

// 验证配置文件一致性
console.log('📋 验证配置文件一致性');

const configFiles = [
  { name: '环境配置', file: '.env' },
  { name: 'Docker Compose配置', file: 'docker-compose.windows.yml' },
  { name: '编辑器Dockerfile', file: 'editor/Dockerfile' },
  { name: '启动脚本', file: 'start-windows.ps1' },
  { name: '停止脚本', file: 'stop-windows.ps1' }
];

configFiles.forEach(config => {
  totalChecks++;
  const filePath = path.join(__dirname, config.file);
  
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${config.name} 存在`);
    passedChecks++;
  } else {
    console.log(`  ❌ ${config.name} 不存在`);
    failedItems.push(`配置文件: ${config.name} 不存在`);
  }
});

console.log('');

// 输出验证结果
console.log('📊 验证结果汇总');
console.log('=' * 50);
console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${passedChecks}`);
console.log(`失败检查: ${totalChecks - passedChecks}`);
console.log(`成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (failedItems.length > 0) {
  console.log('\n❌ 失败项目:');
  failedItems.forEach(item => {
    console.log(`  - ${item}`);
  });
} else {
  console.log('\n🎉 所有检查项都通过了！');
}

// 输出布局调整总结
console.log('\n📝 布局位置调整总结');
console.log('=' * 50);
console.log('✅ 已完成的调整:');
console.log('  1. 将Hierarchy面板移动到左侧上部（标注1→1-1）');
console.log('  2. 将Material Library面板移动到左侧下部');
console.log('  3. 右侧上部现在包含Models和Viewport标签页');
console.log('  4. 右侧下部保持Properties面板');
console.log('  5. 底部面板保持Assets和Visual Scripting标签页（标注2→2-2）');
console.log('  6. 添加了左侧面板区域的样式支持');
console.log('  7. 完善了相关的中文翻译');

console.log('\n📐 新的布局结构:');
console.log('  ┌─────────────┬─────────────────┬─────────────┐');
console.log('  │ 左侧工具栏   │   中间视口区域    │  右侧面板    │');
console.log('  ├─────────────┼─────────────────┼─────────────┤');
console.log('  │ Hierarchy   │                │ Models      │');
console.log('  │ (上部)      │     Viewport    │ Viewport    │');
console.log('  ├─────────────┤                ├─────────────┤');
console.log('  │ Material    │                │ Properties  │');
console.log('  │ Library     │                │             │');
console.log('  │ (下部)      │                │             │');
console.log('  ├─────────────┴─────────────────┴─────────────┤');
console.log('  │        底部面板 (Assets | Visual Scripting)  │');
console.log('  └─────────────────────────────────────────────┘');

console.log('\n🚀 下一步操作:');
console.log('  1. 运行 .\\stop-windows.ps1 停止现有容器');
console.log('  2. 运行 .\\start-windows.ps1 重新启动系统');
console.log('  3. 访问 http://localhost:80 查看修复效果');
console.log('  4. 验证组件位置是否与图片标注一致');

console.log('\n✨ 编辑器布局位置修复验证完成！');

// 返回退出码
process.exit(failedItems.length > 0 ? 1 : 0);
