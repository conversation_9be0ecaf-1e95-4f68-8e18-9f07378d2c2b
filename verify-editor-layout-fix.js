/**
 * 编辑器布局修复验证脚本
 * 验证编辑器布局是否与参考图片一致
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 编辑器布局修复验证开始...\n');

// 验证项目列表
const verificationItems = [
  {
    name: '编辑器主页面布局结构',
    file: 'editor/src/pages/Editor/index.tsx',
    checks: [
      { pattern: /editor-top-section/, description: '上部区域布局容器' },
      { pattern: /right-panel-top/, description: '右侧面板上部' },
      { pattern: /right-panel-bottom/, description: '右侧面板下部' },
      { pattern: /editor-bottom-panel/, description: '底部资源面板' },
      { pattern: /ModelsPanel/, description: 'Models面板组件' },
      { pattern: /MaterialLibraryPanel/, description: 'Material Library面板组件' }
    ]
  },
  {
    name: '编辑器样式文件',
    file: 'editor/src/pages/Editor/Editor.less',
    checks: [
      { pattern: /right-panel-top/, description: '右侧面板上部样式' },
      { pattern: /right-panel-bottom/, description: '右侧面板下部样式' },
      { pattern: /properties-header/, description: '属性面板标题样式' },
      { pattern: /properties-content/, description: '属性面板内容样式' }
    ]
  },
  {
    name: '视口组件顶部工具栏',
    file: 'editor/src/components/Viewport/index.tsx',
    checks: [
      { pattern: /viewport-top-toolbar/, description: '视口顶部工具栏' },
      { pattern: /toolbar-left/, description: '工具栏左侧区域' },
      { pattern: /toolbar-center/, description: '工具栏中间区域' },
      { pattern: /toolbar-right/, description: '工具栏右侧区域' }
    ]
  },
  {
    name: '视口样式文件',
    file: 'editor/src/components/Viewport/Viewport.less',
    checks: [
      { pattern: /viewport-top-toolbar/, description: '顶部工具栏样式' },
      { pattern: /flex-direction: column/, description: '视口垂直布局' },
      { pattern: /flex: 1/, description: '画布自适应高度' }
    ]
  },
  {
    name: 'Models面板组件',
    file: 'editor/src/components/panels/ModelsPanel.tsx',
    checks: [
      { pattern: /ModelsPanel/, description: 'Models面板组件定义' },
      { pattern: /Scene Camera/, description: '场景相机节点' },
      { pattern: /Sky box/, description: '天空盒节点' },
      { pattern: /Point light/, description: '点光源节点' }
    ]
  },
  {
    name: 'Material Library面板组件',
    file: 'editor/src/components/panels/MaterialLibraryPanel.tsx',
    checks: [
      { pattern: /MaterialLibraryPanel/, description: 'Material Library面板组件定义' },
      { pattern: /Metal Steel/, description: '金属材质示例' },
      { pattern: /Wood Oak/, description: '木材材质示例' },
      { pattern: /material-card/, description: '材质卡片样式' }
    ]
  },
  {
    name: '中文翻译文件',
    file: 'editor/src/i18n/locales/zh-CN.json',
    checks: [
      { pattern: /"models": "模型"/, description: 'Models标签翻译' },
      { pattern: /"hierarchy": "层级"/, description: 'Hierarchy标签翻译' },
      { pattern: /"materialLibrary": "材质库"/, description: 'Material Library标签翻译' },
      { pattern: /"properties": "属性"/, description: 'Properties标签翻译' },
      { pattern: /"searchModels": "搜索模型"/, description: '搜索模型翻译' },
      { pattern: /"searchMaterials": "搜索材质"/, description: '搜索材质翻译' }
    ]
  }
];

let totalChecks = 0;
let passedChecks = 0;
let failedItems = [];

// 执行验证
verificationItems.forEach(item => {
  console.log(`📋 验证: ${item.name}`);
  
  const filePath = path.join(__dirname, item.file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${item.file}`);
    failedItems.push(`${item.name}: 文件不存在`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  item.checks.forEach(check => {
    totalChecks++;
    if (check.pattern.test(content)) {
      console.log(`  ✅ ${check.description}`);
      passedChecks++;
    } else {
      console.log(`  ❌ ${check.description}`);
      failedItems.push(`${item.name}: ${check.description}`);
    }
  });
  
  console.log('');
});

// 验证配置文件一致性
console.log('📋 验证配置文件一致性');

const configFiles = [
  { name: '环境配置', file: '.env' },
  { name: 'Docker Compose配置', file: 'docker-compose.windows.yml' },
  { name: '编辑器Dockerfile', file: 'editor/Dockerfile' },
  { name: '启动脚本', file: 'start-windows.ps1' },
  { name: '停止脚本', file: 'stop-windows.ps1' }
];

configFiles.forEach(config => {
  totalChecks++;
  const filePath = path.join(__dirname, config.file);
  
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${config.name} 存在`);
    passedChecks++;
  } else {
    console.log(`  ❌ ${config.name} 不存在`);
    failedItems.push(`配置文件: ${config.name} 不存在`);
  }
});

console.log('');

// 输出验证结果
console.log('📊 验证结果汇总');
console.log('=' * 50);
console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${passedChecks}`);
console.log(`失败检查: ${totalChecks - passedChecks}`);
console.log(`成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (failedItems.length > 0) {
  console.log('\n❌ 失败项目:');
  failedItems.forEach(item => {
    console.log(`  - ${item}`);
  });
} else {
  console.log('\n🎉 所有检查项都通过了！');
}

// 输出布局修复总结
console.log('\n📝 布局修复总结');
console.log('=' * 50);
console.log('✅ 已完成的修复:');
console.log('  1. 调整右侧面板结构为上下两部分');
console.log('  2. 上部为Models/Hierarchy/Material Library标签页');
console.log('  3. 下部为独立的Properties面板');
console.log('  4. 底部面板只覆盖中间和右侧区域');
console.log('  5. 为视口添加顶部工具栏');
console.log('  6. 创建专用的ModelsPanel组件');
console.log('  7. 创建专用的MaterialLibraryPanel组件');
console.log('  8. 完善中文翻译');
console.log('  9. 确保配置文件一致性');

console.log('\n🚀 下一步操作:');
console.log('  1. 运行 .\\stop-windows.ps1 停止现有容器');
console.log('  2. 运行 .\\start-windows.ps1 重新启动系统');
console.log('  3. 访问 http://localhost:80 查看修复效果');
console.log('  4. 登录系统并进入编辑器验证布局');

console.log('\n✨ 编辑器布局修复验证完成！');

// 返回退出码
process.exit(failedItems.length > 0 ? 1 : 0);
