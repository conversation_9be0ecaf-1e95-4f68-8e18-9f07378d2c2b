/**
 * 场景面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { GlobalOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import ScenePanel from './ScenePanel';

// 面板标题组件
const ScenePanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <GlobalOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.scene') || 'Scene'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const ScenePanelTab: TabData = {
  id: 'scenePanel',
  closable: true,
  title: <ScenePanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Scene tab</div>}>
      <Suspense fallback={<div>Loading Scene...</div>}>
        <ScenePanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default ScenePanelTab;
