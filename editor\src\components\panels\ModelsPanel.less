/**
 * 模型面板样式
 */
.models-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #cccccc;

  .panel-header {
    padding: 8px;
    border-bottom: 1px solid #3e3e42;
    flex-shrink: 0;

    .ant-input-affix-wrapper {
      background: #2d2d30;
      border-color: #3e3e42;
      color: #cccccc;

      .ant-input {
        background: transparent;
        color: #cccccc;

        &::placeholder {
          color: #858585;
        }
      }

      .ant-input-prefix {
        color: #858585;
      }

      &:hover,
      &:focus-within {
        border-color: #007acc;
      }
    }

    .ant-btn {
      width: 100%;
      background: #007acc;
      border-color: #007acc;
      color: #fff;

      &:hover {
        background: #005a9e;
        border-color: #005a9e;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 4px;

    .ant-tree {
      background: transparent;
      color: #cccccc;

      .ant-tree-node-content-wrapper {
        color: #cccccc;
        padding: 2px 4px;
        border-radius: 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        &.ant-tree-node-selected {
          background: rgba(0, 122, 204, 0.3);
        }
      }

      .ant-tree-switcher {
        color: #cccccc;
      }

      .ant-tree-iconEle {
        color: #cccccc;
      }
    }

    .model-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 2px 0;

      .node-title {
        flex: 1;
        font-size: 12px;
        color: #cccccc;
      }

      .node-actions {
        opacity: 0;
        transition: opacity 0.2s;

        .ant-btn {
          color: #858585;
          border: none;
          background: transparent;
          padding: 0;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
          }

          &.ant-btn-dangerous:hover {
            color: #ff4d4f;
          }

          .anticon {
            font-size: 10px;
          }
        }
      }

      &:hover .node-actions {
        opacity: 1;
      }
    }
  }

  .ant-empty {
    .ant-empty-description {
      color: #858585;
    }
  }
}
