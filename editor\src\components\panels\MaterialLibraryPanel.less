/**
 * 材质库面板样式
 */
.material-library-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #cccccc;

  .panel-header {
    padding: 8px;
    border-bottom: 1px solid #3e3e42;
    flex-shrink: 0;

    .ant-input-affix-wrapper {
      background: #2d2d30;
      border-color: #3e3e42;
      color: #cccccc;

      .ant-input {
        background: transparent;
        color: #cccccc;

        &::placeholder {
          color: #858585;
        }
      }

      .ant-input-prefix {
        color: #858585;
      }

      &:hover,
      &:focus-within {
        border-color: #007acc;
      }
    }

    .ant-btn {
      width: 100%;
      background: #007acc;
      border-color: #007acc;
      color: #fff;

      &:hover {
        background: #005a9e;
        border-color: #005a9e;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 8px;

    .material-card {
      background: #2d2d30;
      border-color: #3e3e42;
      border-radius: 4px;

      &:hover {
        border-color: #007acc;
      }

      &.selected {
        border-color: #007acc;
        box-shadow: 0 0 0 1px #007acc;
      }

      .ant-card-cover {
        .material-thumbnail {
          height: 60px;
          background: #1e1e1e;
          display: flex;
          align-items: center;
          justify-content: center;

          .material-preview {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            background: linear-gradient(45deg, #333, #666);
            display: flex;
            align-items: center;
            justify-content: center;

            .preview-placeholder {
              color: #fff;
              font-size: 16px;
              font-weight: bold;
            }
          }
        }
      }

      .ant-card-body {
        padding: 8px;

        .ant-card-meta-title {
          color: #cccccc;
          font-size: 12px;
          margin-bottom: 4px;
        }

        .ant-card-meta-description {
          color: #858585;
          font-size: 10px;

          .material-type {
            color: #007acc;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .material-description {
            color: #858585;
            font-size: 9px;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .ant-card-actions {
        background: #252526;
        border-top-color: #3e3e42;

        li {
          margin: 0;

          .anticon {
            color: #858585;
            font-size: 12px;

            &:hover {
              color: #fff;
            }
          }

          &:not(:last-child) {
            border-right-color: #3e3e42;
          }
        }
      }
    }
  }

  .ant-empty {
    .ant-empty-description {
      color: #858585;
    }
  }
}
