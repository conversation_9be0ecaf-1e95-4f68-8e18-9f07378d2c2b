/**
 * 工具栏组件
 */
import React from 'react';
import { Dropdown, Button, Space, Divider, Tooltip } from 'antd';
import {
  PlusOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  SettingOutlined,
  UndoOutlined,
  RedoOutlined,
  Sc<PERSON>orOutlined,
  <PERSON><PERSON>Outlined,
  SnippetsOutlined,
  DeleteOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SelectOutlined,
  EyeOutlined,
  GlobalOutlined,
  BarsOutlined,
  FolderOutlined,
  CodeOutlined,
  DownOutlined,
  UploadOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo
} from '../../store/editor/editorSlice';
import { openDialog, DialogType, PanelType } from '../../store/ui/uiSlice';

interface ToolbarProps {
  onOpenPanel?: (panelType: string) => void;
}

const Toolbar: React.FC<ToolbarProps> = ({ onOpenPanel }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    showGrid,
    showAxes,
    isPlaying
  } = useAppSelector((state) => state.editor);
  
  // 文件菜单
  const fileMenuItems = [
    {
      key: 'new',
      icon: <PlusOutlined />,
      label: t('editor.newProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      icon: <FolderOpenOutlined />,
      label: t('editor.openProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      icon: <SaveOutlined />,
      label: t('editor.saveProject')
    },
    {
      key: 'saveAs',
      icon: <SaveOutlined />,
      label: t('editor.saveProjectAs'),
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'import',
      icon: <ImportOutlined />,
      label: t('editor.importAsset'),
      onClick: () => dispatch(openDialog({ type: DialogType.IMPORT_ASSET, title: t('editor.importAsset') }))
    },
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: t('editor.exportScene'),
      onClick: () => dispatch(openDialog({ type: DialogType.EXPORT_SCENE, title: t('editor.exportScene') }))
    },
    { type: 'divider' as const },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('editor.projectSettings'),
      onClick: () => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))
    }
  ];
  
  // 编辑菜单
  const editMenuItems = [
    {
      key: 'undo',
      icon: <UndoOutlined />,
      label: t('editor.undo'),
      onClick: () => dispatch(undo())
    },
    {
      key: 'redo',
      icon: <RedoOutlined />,
      label: t('editor.redo'),
      onClick: () => dispatch(redo())
    },
    { type: 'divider' as const },
    {
      key: 'cut',
      icon: <ScissorOutlined />,
      label: t('editor.cut')
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: t('editor.copy')
    },
    {
      key: 'paste',
      icon: <SnippetsOutlined />,
      label: t('editor.paste')
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: t('editor.delete')
    }
  ];
  
  // 视图菜单
  const viewMenuItems = [
    {
      key: 'grid',
      icon: <BorderOutlined />,
      label: showGrid ? t('editor.hideGrid') : t('editor.showGrid'),
      onClick: () => dispatch(setShowGrid(!showGrid))
    },
    {
      key: 'axes',
      icon: <AppstoreOutlined />,
      label: showAxes ? t('editor.hideAxes') : t('editor.showAxes'),
      onClick: () => dispatch(setShowAxes(!showAxes))
    },
    { type: 'divider' as const },
    {
      key: 'sceneView',
      icon: <EyeOutlined />,
      label: t('editor.sceneView')
    },
    {
      key: 'gameView',
      icon: <PlayCircleOutlined />,
      label: t('editor.gameView')
    }
  ];
  
  // 工具菜单
  const toolsMenuItems = [
    {
      key: 'select',
      icon: <SelectOutlined />,
      label: t('editor.selectTool')
    },
    {
      key: 'translate',
      icon: <ArrowsAltOutlined />,
      label: t('editor.translateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.TRANSLATE))
    },
    {
      key: 'rotate',
      icon: <RotateRightOutlined />,
      label: t('editor.rotateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.ROTATE))
    },
    {
      key: 'scale',
      icon: <ColumnWidthOutlined />,
      label: t('editor.scaleTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.SCALE))
    },
    { type: 'divider' as const },
    {
      key: 'transformSpace',
      label: t('editor.transformSpace'),
      children: [
        {
          key: 'local',
          label: t('editor.localSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.LOCAL))
        },
        {
          key: 'world',
          label: t('editor.worldSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.WORLD))
        }
      ]
    },
    {
      key: 'snapMode',
      label: t('editor.snapMode'),
      children: [
        {
          key: 'disabled',
          label: t('editor.snapDisabled'),
          onClick: () => dispatch(setSnapMode(SnapMode.DISABLED))
        },
        {
          key: 'grid',
          label: t('editor.snapToGrid'),
          onClick: () => dispatch(setSnapMode(SnapMode.GRID))
        },
        {
          key: 'vertex',
          label: t('editor.snapToVertex'),
          onClick: () => dispatch(setSnapMode(SnapMode.VERTEX))
        }
      ]
    }
  ];
  
  // 窗口菜单
  const windowMenuItems = [
    {
      key: 'terrain',
      icon: <GlobalOutlined />,
      label: t('editor.terrain.terrainEditor') || '地形编辑器',
      onClick: () => onOpenPanel?.(PanelType.TERRAIN)
    },
    { type: 'divider' as const },
    {
      key: 'hierarchy',
      icon: <BarsOutlined />,
      label: t('editor.hierarchyView') || '层级面板',
      onClick: () => onOpenPanel?.(PanelType.HIERARCHY)
    },
    {
      key: 'assets',
      icon: <FolderOutlined />,
      label: t('editor.assetsView') || '资源面板',
      onClick: () => onOpenPanel?.(PanelType.ASSETS)
    },
    {
      key: 'console',
      icon: <CodeOutlined />,
      label: t('editor.consoleView') || '控制台',
      onClick: () => onOpenPanel?.(PanelType.CONSOLE)
    }
  ];

  // 帮助菜单
  const helpMenuItems = [
    {
      key: 'documentation',
      label: t('editor.documentation')
    },
    {
      key: 'tutorials',
      label: t('editor.tutorials')
    },
    {
      key: 'about',
      label: t('editor.about')
    }
  ];
  
  return (
    <div style={{
      display: 'flex',
      height: '40px',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 16px',
      background: '#1e1e1e'
    }}>
      {/* 左侧：Logo和主菜单 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ cursor: 'pointer' }} onClick={() => window.history.back()}>
          <AppstoreOutlined style={{ color: '#fff', fontSize: '16px', marginRight: '8px' }} />
        </div>
        <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
          <Button type="text" style={{ color: '#fff', padding: '0 8px' }}>
            <DownOutlined style={{ fontSize: '12px' }} />
          </Button>
        </Dropdown>
      </div>

      {/* 中间：项目面包屑 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '10px', color: '#fff' }}>
        <FileOutlined style={{ fontSize: '14px' }} />
        <span style={{ color: '#8b949e' }}>DL-Engine</span>
        <span style={{ color: '#8b949e' }}> / </span>
        <span style={{ color: '#f0f6fc' }}>Electronics Demo Project</span>
      </div>

      {/* 右侧：用户信息和发布按钮 */}
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
        {/* 用户头像占位 */}
        <div style={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
          background: '#007acc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          fontSize: '12px'
        }}>
          U
        </div>

        {/* 发布按钮 */}
        <Button
          type="primary"
          style={{
            background: '#007acc',
            borderColor: '#007acc',
            borderRadius: '8px',
            padding: '4px 12px',
            fontSize: '12px',
            height: '28px'
          }}
        >
          <UploadOutlined style={{ fontSize: '12px', marginRight: '4px' }} />
          {t('editor.publish') || 'Publish'}
        </Button>
      </div>
    </div>
  );
};

export default Toolbar;
