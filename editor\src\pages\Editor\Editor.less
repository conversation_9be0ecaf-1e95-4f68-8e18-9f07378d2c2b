/**
 * 编辑器样式
 * 采用经典的四区域布局
 */
.editor-layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #1e1e1e;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-horizontal {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧面板区域
.editor-left-panels {
  width: 250px;
  background: #252526;
  border-right: 1px solid #3e3e42;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .left-panel-top {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 200px;
    border-bottom: 1px solid #3e3e42;

    .panel-header {
      background: #2d2d30;
      padding: 8px 12px;
      border-bottom: 1px solid #3e3e42;
      flex-shrink: 0;

      h4 {
        margin: 0;
        color: #cccccc;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .panel-content {
      flex: 1;
      overflow: auto;
      background: #1e1e1e;
    }
  }

  .left-panel-bottom {
    height: 300px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .panel-header {
      background: #2d2d30;
      padding: 8px 12px;
      border-bottom: 1px solid #3e3e42;
      flex-shrink: 0;

      h4 {
        margin: 0;
        color: #cccccc;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .panel-content {
      flex: 1;
      overflow: auto;
      background: #1e1e1e;
    }
  }
}

.editor-center-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 上部：中间视口和右侧面板的水平布局
  .editor-top-section {
    flex: 1;
    display: flex;
    overflow: hidden;

    .editor-viewport {
      flex: 1;
      position: relative;
      background: #1e1e1e;
      overflow: hidden;
    }
  }

  // 底部面板只覆盖中间和右侧区域
  .editor-bottom-panel {
    width: 100%;
    border-top: 1px solid #3e3e42;
    background: #252526;
    overflow: hidden;
  }
}

.editor-right-panel {
  width: 300px;
  background: #252526;
  border-left: 1px solid #3e3e42;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 上部标签页面板
  .right-panel-top {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 200px;

    .right-panel-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-tabs-nav {
        background: #2d2d30;
        margin: 0;
        padding: 0 8px;
        flex-shrink: 0;

        .ant-tabs-tab {
          color: #cccccc;
          padding: 6px 12px;
          font-size: 12px;

          &:hover {
            color: #fff;
          }

          &.ant-tabs-tab-active {
            color: #fff;

            .ant-tabs-tab-btn {
              color: #fff;
            }
          }
        }

        .ant-tabs-ink-bar {
          background: #007acc;
        }
      }

      .ant-tabs-content-holder {
        background: #1e1e1e;
        flex: 1;
        overflow: hidden;

        .ant-tabs-content {
          height: 100%;

          .ant-tabs-tabpane {
            height: 100%;
            overflow: hidden;
          }
        }
      }
    }

    .panel-content {
      padding: 8px;
      height: 100%;
      overflow: auto;
      background: #1e1e1e;
      color: #cccccc;
    }
  }

  // 下部属性面板
  .right-panel-bottom {
    height: 300px;
    border-top: 1px solid #3e3e42;
    background: #1e1e1e;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .properties-header {
      background: #2d2d30;
      padding: 8px 12px;
      border-bottom: 1px solid #3e3e42;
      flex-shrink: 0;

      h4 {
        margin: 0;
        color: #cccccc;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .properties-content {
      flex: 1;
      overflow: auto;
      background: #1e1e1e;
    }
  }
}

// 全局底部面板样式（已移动到 .editor-center-right 内部）
