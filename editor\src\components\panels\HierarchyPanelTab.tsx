/**
 * 层级面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { BarsOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import HierarchyPanel from './HierarchyPanel';

// 面板标题组件
const HierarchyPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <BarsOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.hierarchy') || 'Hierarchy'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const HierarchyPanelTab: TabData = {
  id: 'hierarchyPanel',
  closable: true,
  title: <HierarchyPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Hierarchy tab</div>}>
      <Suspense fallback={<div>Loading Hierarchy...</div>}>
        <HierarchyPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default HierarchyPanelTab;
