/**
 * 材质库面板组件
 * 显示和管理材质资源
 */
import React, { useState } from 'react';
import { Card, Row, Col, Input, Button, Empty, Tooltip, Modal } from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './MaterialLibraryPanel.less';

const { Search } = Input;
const { Meta } = Card;

interface Material {
  id: string;
  name: string;
  type: string;
  thumbnail?: string;
  description?: string;
}

const MaterialLibraryPanel: React.FC = () => {
  const { t } = useTranslation();
  const [searchValue, setSearchValue] = useState('');
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);

  // 示例材质数据
  const materials: Material[] = [
    {
      id: '1',
      name: 'Metal Steel',
      type: 'PBR',
      description: 'Realistic steel material with PBR textures'
    },
    {
      id: '2',
      name: 'Wood Oak',
      type: 'PBR',
      description: 'Natural oak wood texture'
    },
    {
      id: '3',
      name: 'Concrete',
      type: 'PBR',
      description: 'Rough concrete surface'
    },
    {
      id: '4',
      name: 'Glass Clear',
      type: 'Transparent',
      description: 'Clear glass material'
    }
  ];

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 实现搜索逻辑
  };

  const handleMaterialClick = (materialId: string) => {
    setSelectedMaterial(materialId);
    console.log('Selected material:', materialId);
  };

  const handleAddMaterial = () => {
    console.log('Add new material');
  };

  const handleEditMaterial = (materialId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Edit material:', materialId);
  };

  const handleDeleteMaterial = (materialId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    Modal.confirm({
      title: t('editor.confirmDelete') || '确认删除',
      content: t('editor.deleteMaterialConfirm') || '确定要删除这个材质吗？',
      okText: t('common.delete') || '删除',
      okType: 'danger',
      cancelText: t('common.cancel') || '取消',
      onOk() {
        console.log('Delete material:', materialId);
      }
    });
  };

  const handlePreviewMaterial = (materialId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Preview material:', materialId);
  };

  const handleDownloadMaterial = (materialId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Download material:', materialId);
  };

  const filteredMaterials = materials.filter(material =>
    material.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    material.type.toLowerCase().includes(searchValue.toLowerCase())
  );

  return (
    <div className="material-library-panel">
      <div className="panel-header">
        <Search
          placeholder={t('editor.searchMaterials') || '搜索材质'}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ marginBottom: 8 }}
          prefix={<SearchOutlined />}
        />
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAddMaterial}
          style={{ marginBottom: 8 }}
        >
          {t('editor.addMaterial') || '添加材质'}
        </Button>
      </div>

      <div className="panel-content">
        {filteredMaterials.length > 0 ? (
          <Row gutter={[8, 8]}>
            {filteredMaterials.map((material) => (
              <Col span={12} key={material.id}>
                <Card
                  hoverable
                  size="small"
                  className={`material-card ${selectedMaterial === material.id ? 'selected' : ''}`}
                  onClick={() => handleMaterialClick(material.id)}
                  cover={
                    <div className="material-thumbnail">
                      <div className="material-preview">
                        {/* 这里可以放置材质预览图 */}
                        <div className="preview-placeholder">
                          {material.name.charAt(0)}
                        </div>
                      </div>
                    </div>
                  }
                  actions={[
                    <Tooltip title={t('editor.preview') || '预览'} key="preview">
                      <EyeOutlined onClick={(e) => handlePreviewMaterial(material.id, e)} />
                    </Tooltip>,
                    <Tooltip title={t('editor.edit') || '编辑'} key="edit">
                      <EditOutlined onClick={(e) => handleEditMaterial(material.id, e)} />
                    </Tooltip>,
                    <Tooltip title={t('editor.download') || '下载'} key="download">
                      <DownloadOutlined onClick={(e) => handleDownloadMaterial(material.id, e)} />
                    </Tooltip>,
                    <Tooltip title={t('editor.delete') || '删除'} key="delete">
                      <DeleteOutlined onClick={(e) => handleDeleteMaterial(material.id, e)} />
                    </Tooltip>
                  ]}
                >
                  <Meta
                    title={material.name}
                    description={
                      <div>
                        <div className="material-type">{material.type}</div>
                        <div className="material-description">{material.description}</div>
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('editor.noMaterials') || '暂无材质'}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    </div>
  );
};

export default MaterialLibraryPanel;
