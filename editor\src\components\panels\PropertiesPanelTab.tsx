/**
 * 属性面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { SettingOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import InspectorPanel from './InspectorPanel';

// 面板标题组件
const PropertiesPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <SettingOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.properties') || 'Properties'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const PropertiesPanelTab: TabData = {
  id: 'propertiesPanel',
  closable: true,
  title: <PropertiesPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Properties tab</div>}>
      <Suspense fallback={<div>Loading Properties...</div>}>
        <InspectorPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default PropertiesPanelTab;
