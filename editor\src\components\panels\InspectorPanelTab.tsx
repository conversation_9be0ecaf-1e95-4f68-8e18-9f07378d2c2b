/**
 * 检查器面板Tab组件 - 参考ir-engine-dev实现
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { EyeOutlined } from '@ant-design/icons';
import { ErrorBoundary } from '../common/ErrorBoundary';
import InspectorPane<PERSON> from './InspectorPanel';

// 面板标题组件
const InspectorPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      padding: '0 8px',
      cursor: 'move',
      userSelect: 'none',
      gap: '6px'
    }}>
      <EyeOutlined style={{ fontSize: '14px' }} />
      <span>{t('editor.inspector') || 'Inspector'}</span>
    </div>
  );
};

// 导出TabData格式的面板配置
export const InspectorPanelTab: TabData = {
  id: 'inspectorPanel',
  closable: true,
  title: <InspectorPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Inspector tab</div>}>
      <Suspense fallback={<div>Loading Inspector...</div>}>
        <InspectorPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

export default InspectorPanelTab;
