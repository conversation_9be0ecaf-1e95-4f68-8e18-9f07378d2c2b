/* 编辑器容器样式 - 参考ir-engine-dev实现 */

.dock-container {
  height: 100%;
  width: 100%;
  position: relative;
}

/* rc-dock 样式定制 */
.dock-layout {
  background: #1e1e1e;
  color: #ffffff;
}

.dock-panel {
  background: #2d2d30;
  border: 1px solid #3e3e42;
}

.dock-tab {
  background: #2d2d30;
  border: none;
  color: #cccccc;
  padding: 8px 12px;
  font-size: 12px;
  border-radius: 0;
}

.dock-tab:hover {
  background: #3e3e42;
  color: #ffffff;
}

.dock-tab-active {
  background: #007acc !important;
  color: #ffffff !important;
}

.dock-tab-close-btn {
  color: #cccccc;
  margin-left: 6px;
}

.dock-tab-close-btn:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.dock-tabbar {
  background: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  min-height: 32px;
}

.dock-content {
  background: #1e1e1e;
  color: #cccccc;
  padding: 0;
  overflow: hidden;
}

.dock-divider {
  background: rgba(255, 255, 255, var(--dividerAlpha, 0.1));
}

.dock-divider:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 面板拖拽样式 */
.dock-panel-drag-over {
  background: rgba(0, 122, 204, 0.2);
}

.dock-panel-drag-over-edge {
  background: rgba(0, 122, 204, 0.4);
}

/* 面板最大化样式 */
.dock-panel-max {
  background: #1e1e1e;
}

/* 面板标题样式 */
.dock-panel-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #cccccc;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dock-tab {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .dock-tabbar {
    min-height: 28px;
  }
}

/* 深色主题优化 */
.dark .dock-layout {
  background: #0d1117;
}

.dark .dock-panel {
  background: #161b22;
  border-color: #30363d;
}

.dark .dock-tab {
  background: #161b22;
  color: #8b949e;
}

.dark .dock-tab:hover {
  background: #21262d;
  color: #f0f6fc;
}

.dark .dock-tab-active {
  background: #1f6feb !important;
  color: #f0f6fc !important;
}

.dark .dock-tabbar {
  background: #161b22;
  border-bottom-color: #30363d;
}

.dark .dock-content {
  background: #0d1117;
  color: #8b949e;
}

/* 浅色主题 */
.light .dock-layout {
  background: #ffffff;
}

.light .dock-panel {
  background: #f6f8fa;
  border-color: #d0d7de;
}

.light .dock-tab {
  background: #f6f8fa;
  color: #656d76;
}

.light .dock-tab:hover {
  background: #eaeef2;
  color: #24292f;
}

.light .dock-tab-active {
  background: #0969da !important;
  color: #ffffff !important;
}

.light .dock-tabbar {
  background: #f6f8fa;
  border-bottom-color: #d0d7de;
}

.light .dock-content {
  background: #ffffff;
  color: #24292f;
}

/* 面板内容区域样式 */
.panel-content {
  height: 100%;
  overflow: auto;
  padding: 8px;
}

.panel-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 工具提示样式 */
.dock-tooltip {
  background: #2d2d30;
  color: #cccccc;
  border: 1px solid #3e3e42;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  z-index: 1000;
}

/* 动画效果 */
.dock-tab {
  transition: background-color 0.2s ease, color 0.2s ease;
}

.dock-divider {
  transition: background-color 0.2s ease;
}

.dock-panel {
  transition: border-color 0.2s ease;
}

/* 焦点样式 */
.dock-tab:focus {
  outline: 2px solid #007acc;
  outline-offset: -2px;
}

/* 禁用状态 */
.dock-tab-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dock-tab-disabled:hover {
  background: inherit;
  color: inherit;
}
