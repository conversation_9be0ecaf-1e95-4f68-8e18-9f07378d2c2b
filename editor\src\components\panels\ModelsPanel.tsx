/**
 * 模型面板组件
 * 显示场景中的3D模型和对象
 */
import React, { useState } from 'react';
import { Tree, Input, Button, Empty, Space, Tooltip } from 'antd';
import {
  AppstoreOutlined,
  SearchOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './ModelsPanel.less';

const { Search } = Input;
const { DirectoryTree } = Tree;

interface ModelNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: ModelNode[];
  visible?: boolean;
  locked?: boolean;
  type?: string;
}

const ModelsPanel: React.FC = () => {
  const { t } = useTranslation();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['scene']);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');

  // 示例模型数据
  const modelData: ModelNode[] = [
    {
      key: 'scene',
      title: 'Scene',
      icon: <AppstoreOutlined />,
      children: [
        {
          key: 'scene-camera',
          title: 'Scene Camera',
          icon: <AppstoreOutlined />,
          visible: true,
          locked: false,
          type: 'camera'
        },
        {
          key: 'sky-box',
          title: 'Sky box',
          icon: <AppstoreOutlined />,
          visible: true,
          locked: false,
          type: 'skybox'
        },
        {
          key: 'point-light',
          title: 'Point light',
          icon: <AppstoreOutlined />,
          visible: true,
          locked: false,
          type: 'light'
        }
      ]
    }
  ];

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 实现搜索逻辑
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedKeys(selectedKeys);
    console.log('Selected model:', selectedKeys, info);
  };

  const handleExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  const handleToggleVisibility = (nodeKey: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Toggle visibility for:', nodeKey);
  };

  const handleToggleLock = (nodeKey: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Toggle lock for:', nodeKey);
  };

  const handleDelete = (nodeKey: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Delete model:', nodeKey);
  };

  const handleAddModel = () => {
    console.log('Add new model');
  };

  // 自定义节点渲染
  const renderTreeNode = (nodeData: any) => {
    return (
      <div className="model-node">
        <span className="node-title">{nodeData.title}</span>
        <Space className="node-actions" size={2}>
          <Tooltip title={nodeData.visible ? '隐藏' : '显示'}>
            <Button
              type="text"
              size="small"
              icon={nodeData.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={(e) => handleToggleVisibility(nodeData.key, e)}
            />
          </Tooltip>
          <Tooltip title={nodeData.locked ? '解锁' : '锁定'}>
            <Button
              type="text"
              size="small"
              icon={nodeData.locked ? <LockOutlined /> : <UnlockOutlined />}
              onClick={(e) => handleToggleLock(nodeData.key, e)}
            />
          </Tooltip>
          {nodeData.type !== 'scene' && (
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => handleDelete(nodeData.key, e)}
              />
            </Tooltip>
          )}
        </Space>
      </div>
    );
  };

  return (
    <div className="models-panel">
      <div className="panel-header">
        <Search
          placeholder={t('editor.searchModels') || '搜索模型'}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ marginBottom: 8 }}
          prefix={<SearchOutlined />}
        />
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAddModel}
          style={{ marginBottom: 8 }}
        >
          {t('editor.addModel') || '添加模型'}
        </Button>
      </div>

      <div className="panel-content">
        {modelData.length > 0 ? (
          <DirectoryTree
            showIcon
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={handleExpand}
            onSelect={handleSelect}
            titleRender={renderTreeNode}
            treeData={modelData}
            blockNode
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('editor.noModels') || '暂无模型'}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    </div>
  );
};

export default ModelsPanel;
